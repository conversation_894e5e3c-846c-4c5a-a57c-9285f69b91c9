import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, CheckCircle, 
  ArrowRight, Briefcase, Users, Mail, Phone, Home, User
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { supabase } from '../../../../lib/supabase/client';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface OfficeBookingFormData {
  officeType: string;
  officeSize: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedOfficeForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<OfficeBookingFormData>>({
    officeType: 'standard',
    officeSize: 'medium',
    serviceFrequency: 'weekly',
    addOns: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();
  
  useEffect(() => {
    const savedData = localStorage.getItem('officeFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('officeFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Service Type' },
    { id: 2, name: 'Office & Schedule' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const officeTypes = [
    { id: 'standard', name: 'Standard Office', icon: <Building className="w-8 h-8" />, description: 'General-purpose office spaces' },
    { id: 'coworking', name: 'Co-working Space', icon: <Users className="w-8 h-8" />, description: 'Shared and flexible workspaces' },
    { id: 'medical', name: 'Medical Office', icon: <Briefcase className="w-8 h-8" />, description: 'Clinics and healthcare facilities' },
    { id: 'tech-startup', name: 'Tech Startup', icon: <Sparkles className="w-8 h-8" />, description: 'Fast-paced startup environments' },
  ];

  const officeSizeOptions = [
    { id: 'small', name: 'Under 1,000 sq ft' },
    { id: 'medium', name: '1,000 - 5,000 sq ft' },
    { id: 'large', name: '5,000 - 10,000 sq ft' },
    { id: 'xl', name: '10,000+ sq ft' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time' },
    { id: 'daily', name: 'Daily' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
  ];

  const addOnServices = [
    { id: 'window-cleaning', name: 'Interior Window Cleaning' },
    { id: 'carpet-shampoo', name: 'Carpet Shampooing' },
    { id: 'high-touch-sanitization', name: 'High-Touch Sanitization' },
    { id: 'restroom-restocking', name: 'Restroom Supply Restocking' },
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' },
  ];
  
  // Form validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!formData.officeType;
      case 2:
        return !!(formData.officeSize && formData.serviceFrequency && formData.preferredDate && formData.preferredTime);
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.companyName && 
          formData.contactName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode &&
          validateEmail(formData.email) &&
          validatePhone(formData.phone)
        );
      default:
        return false;
    }
  };

  // Calculate base price (simplified for office cleaning)
  const calculateTotalPrice = (): number => {
    let basePrice = 150; // Base office cleaning price
    
    // Adjust by office size
    const sizeMultipliers: { [key: string]: number } = {
      'small': 0.8,
      'medium': 1.0,
      'large': 1.5,
      'xl': 2.0
    };
    
    const sizeMultiplier = sizeMultipliers[formData.officeSize || 'medium'] || 1.0;
    basePrice *= sizeMultiplier;
    
    // Add-on prices
    const addOnPrices: { [key: string]: number } = {
      'window-cleaning': 50,
      'carpet-shampoo': 75,
      'high-touch-sanitization': 40,
      'restroom-restocking': 25
    };
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      return total + (addOnPrices[addOnId] || 0);
    }, 0);
    
    return Math.round(basePrice + addOnTotal);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: 'commercial_office',
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('commercialOfficeBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      // Prepare booking data for database
      const bookingData = {
        user_id: user?.id,
        service_type: 'commercial_office',
        status: 'pending',
        contact: {
          companyName: formData.companyName,
          contactName: formData.contactName,
          email: formData.email,
          phone: formData.phone
        },
        office_details: {
          type: formData.officeType,
          size: formData.officeSize,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: formData.serviceFrequency,
          addOns: formData.addOns || [],
          totalPrice: calculateTotalPrice(),
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'office_cleaning_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Save to database
      const { error } = await supabase
        .from('bookings')
        .insert([ServiceTypeStandardizer.standardizeBookingData(bookingData)]);

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      // Clear form data
      localStorage.removeItem('officeFormData');
      
      // Navigate to thank you page
      navigate('/thank-you', {
        state: {
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `OFC-${Date.now()}`,
            confirmationNumber: `OFC-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Office Cleaning',
          bookingDetails: {
            id: `OFC-${Date.now()}`,
            type: 'Office Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    } catch (error) {
      console.error('Booking completion error:', error);
      alert('There was an error completing your booking. Please contact support.');
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Professional Office Cleaning
            </h1>
            <p className="text-gray-600">Tailored cleaning solutions for your business environment.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-white" style={{ color: 'white' }} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Office Type</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {officeTypes.map(type => (
                      <motion.button 
                        key={type.id} 
                        onClick={() => setFormData({...formData, officeType: type.id})} 
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${formData.officeType === type.id ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'}`}
                      >
                        <div className="flex items-center gap-4">
                          <div className={`p-2 rounded-lg transition-colors duration-200 ${formData.officeType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'}`}>
                            {type.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{type.name}</h3>
                            <p className="text-sm text-gray-600">{type.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}
               {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Office & Schedule Details</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                     <GlassmorphismSelect
                        options={officeSizeOptions}
                        value={formData.officeSize}
                        onChange={value => setFormData({...formData, officeSize: value})}
                        placeholder="Select Size"
                      />
                     <GlassmorphismSelect
                        options={serviceFrequencies}
                        value={formData.serviceFrequency}
                        onChange={value => setFormData({...formData, serviceFrequency: value})}
                        placeholder="Select Frequency"
                      />
                     <input 
                       type="date" 
                       value={formData.preferredDate || ''} 
                       onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                       className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                     />
                     <GlassmorphismSelect
                        options={timeSlots}
                        value={formData.preferredTime}
                        onChange={value => setFormData({...formData, preferredTime: value})}
                        placeholder="Select Time"
                      />
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Add-on Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button 
                        key={addon.id} 
                        onClick={() => handleAddOnToggle(addon.id)} 
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${(formData.addOns || []).includes(addon.id) ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'}`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-900">{addon.name}</span>
                          {(formData.addOns || []).includes(addon.id) && (
                            <CheckCircle className="w-5 h-5 text-emerald-600" />
                          )}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Company Name" value={formData.companyName || ''} onChange={e => setFormData({...formData, companyName: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Contact Name" value={formData.contactName || ''} onChange={e => setFormData({...formData, contactName: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="email" placeholder="Email" value={formData.email || ''} onChange={e => setFormData({...formData, email: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="tel" placeholder="Phone" value={formData.phone || ''} onChange={e => setFormData({...formData, phone: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Address" value={formData.address || ''} onChange={e => setFormData({...formData, address: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <input type="text" placeholder="City" value={formData.city || ''} onChange={e => setFormData({...formData, city: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    <input type="text" placeholder="ZIP Code" value={formData.zipCode || ''} onChange={e => setFormData({...formData, zipCode: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                  </div>
                  <textarea placeholder="Special Instructions" value={formData.specialInstructions || ''} onChange={e => setFormData({...formData, specialInstructions: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" rows={4} />
                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculateTotalPrice()}
          description="Commercial Office Cleaning"
          customerEmail={formData.email}
          formData={{
            ...formData,
            totalPrice: calculateTotalPrice(),
            serviceType: 'commercial_office'
          }}
          user={user}
          onPaymentComplete={handlePaymentComplete}
        />
      )}
    </AnimatedBackground>
  );
};

export default BrandAlignedOfficeForm; 
